import {
  require_baseGet
} from "./chunk-ILQTQNEH.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/get.js
var require_get = __commonJS({
  "node_modules/lodash/get.js"(exports, module) {
    var baseGet = require_baseGet();
    function get(object, path, defaultValue) {
      var result = object == null ? void 0 : baseGet(object, path);
      return result === void 0 ? defaultValue : result;
    }
    module.exports = get;
  }
});

export {
  require_get
};
//# sourceMappingURL=chunk-G4H6LCRN.js.map
