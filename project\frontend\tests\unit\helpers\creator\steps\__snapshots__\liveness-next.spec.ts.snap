// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`CreatorStepLivenessNext (builder) > should build schema to match the previous snapshot, locale=en 1`] = `
{
  "builder": {
    "type": "ekyc_liveness_next",
  },
  "fields": {
    "ekyc_liveness_item": "ekyc_liveness",
  },
  "name": "ekyc_liveness_next",
  "sections": {
    "ekyc_liveness_next": {
      "description": "Description",
      "hide_description": true,
      "hide_label": false,
      "hide_subheading": false,
      "items": {
        "ekyc_liveness": {
          "auto_next": true,
          "builder": {
            "type": "ekyc_liveness_item_next",
          },
          "display": {
            "label": "",
          },
          "layout": "InputControl",
          "liveness": {
            "action_sequence": [],
            "enableFaceSize": true,
            "enableIdleOnly": true,
          },
          "max_attempt_warning": {
            "allow_max_attempt_pass": false,
            "button": {
              "auto_redirect": false,
              "label": "Go to Home Page",
              "target": "/page/test",
              "visible": true,
            },
            "description": "Please contact the customer support.",
            "max_attempt_count": 5,
            "title": "Liveness verification failed",
          },
          "name": "ekyc_liveness",
          "type": "Ekyc.LivenessVNext",
          "validator_rule": "required",
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
      },
      "label": "",
      "name": "ekyc_liveness_next",
      "post_save": undefined,
      "pre_save": undefined,
      "subheading": "",
    },
  },
  "styling": {
    "--form-section-header-align": "center",
    "--form-step-header-align": "center",
    "--form-step-header-size": "1.375rem",
  },
  "type": "Ekyc",
  "visible": undefined,
}
`;

exports[`CreatorStepLivenessNext (builder) > should build schema to match the previous snapshot, locale=th 1`] = `
{
  "builder": {
    "type": "ekyc_liveness_next",
  },
  "fields": {
    "ekyc_liveness_item": "ekyc_liveness",
  },
  "name": "ekyc_liveness_next",
  "sections": {
    "ekyc_liveness_next": {
      "description": "Description",
      "hide_description": true,
      "hide_label": false,
      "hide_subheading": false,
      "items": {
        "ekyc_liveness": {
          "auto_next": true,
          "builder": {
            "type": "ekyc_liveness_item_next",
          },
          "display": {
            "label": "",
          },
          "layout": "InputControl",
          "liveness": {
            "action_sequence": [],
            "enableFaceSize": true,
            "enableIdleOnly": true,
          },
          "max_attempt_warning": {
            "allow_max_attempt_pass": false,
            "button": {
              "auto_redirect": false,
              "label": "กลับหน้าหลัก",
              "target": "/page/test",
              "visible": true,
            },
            "description": "กรุณาติดต่อฝ่ายบริการลูกค้า",
            "max_attempt_count": 5,
            "title": "ไม่สามารถตรวจสอบการยืนยันตัวตนได้",
          },
          "name": "ekyc_liveness",
          "type": "Ekyc.LivenessVNext",
          "validator_rule": "required",
          "visible": undefined,
          "visible_flag_invert": undefined,
        },
      },
      "label": "",
      "name": "ekyc_liveness_next",
      "post_save": undefined,
      "pre_save": undefined,
      "subheading": "",
    },
  },
  "styling": {
    "--form-section-header-align": "center",
    "--form-step-header-align": "center",
    "--form-step-header-size": "1.375rem",
  },
  "type": "Ekyc",
  "visible": undefined,
}
`;
