// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Report Schema Generator > should build schema to match the previous snapshot 1`] = `
{
  "configs": {
    "disabled_sidebar": false,
  },
  "steps": {
    "AML": {
      "name": "AML",
      "sections": {
        "AML": {
          "items": {},
          "label": "AML",
          "name": "AML",
          "span": 12,
          "type": "Aml",
        },
      },
      "tab": "AML",
    },
    "ActivityLog": {
      "name": "ActivityLog",
      "sections": {
        "Activity Log": {
          "items": {},
          "label": "Activity Log",
          "name": "Activity Log",
          "span": 12,
          "type": "ActivityLog",
        },
      },
      "tab": "Activity Log",
    },
    "BankStatement": {
      "fields": {
        "bank_statement": "bank_statement_uploader",
      },
      "name": "BankStatement",
      "sections": {},
      "tab": "Bank Statement",
      "type": "BankStatement",
    },
    "Business": {
      "name": "Business",
      "sections": {
        "Business": {
          "items": {},
          "label": "Business",
          "name": "Business",
          "span": 12,
          "type": "Business",
        },
      },
      "tab": "Business",
    },
    "DecisionFlow": {
      "name": "DecisionFlow",
      "sections": {
        "Decision Flow": {
          "items": {},
          "label": "Decision Flow",
          "name": "Decision Flow",
          "span": 12,
          "type": "Decision",
        },
      },
      "tab": "Decision Flow",
    },
    "Identity": {
      "name": "Identity",
      "sections": {
        "Identity": {
          "items": {},
          "label": "Identity",
          "name": "Identity",
          "span": 12,
          "type": "Identity",
        },
      },
      "tab": "Identity",
    },
    "Integration": {
      "name": "Integration",
      "sections": {
        "Integration": {
          "items": {},
          "label": "Integration",
          "name": "Integration",
          "span": 12,
          "type": "Integration",
        },
      },
      "tab": "Integration",
    },
    "LED": {
      "name": "LED",
      "sections": {
        "LED": {
          "items": {},
          "label": "LED",
          "name": "LED",
          "span": 12,
          "type": "Led",
        },
      },
      "tab": "LED",
    },
    "Response": {
      "label": "Response",
      "name": "Response",
      "sections": {
        "ekyc_document": {
          "can_edit": true,
          "children_span": 6,
          "icon": "lucide:file-text",
          "item_names": [
            "ekyc_document_country",
            "ekyc_document_type",
            "nid",
            "document_number",
            "name_prefix",
            "full_name_first_name",
            "full_name_middle_name",
            "full_name_last_name",
            "full_name_full",
            "full_name_type",
            "full_name_show_middle_name",
            "gender",
            "home_address",
            "date_of_birth",
            "date_of_issue",
            "date_of_expiry",
          ],
          "items": {
            "ekyc_document_country": {
              "can_edit": false,
            },
            "ekyc_document_type": {
              "can_edit": false,
            },
            "home_address": {
              "span": 12,
            },
          },
          "label": "ID Document Verification",
          "span": 12,
          "type": "TableCard",
        },
        "email": {
          "can_edit": true,
          "children_span": 6,
          "icon": "lucide:file-text",
          "item_names": [
            "email_otp_address_email",
          ],
          "items": {},
          "label": "Email Verification",
          "span": 12,
          "type": "TableCard",
        },
        "personal": {
          "can_edit": true,
          "children_span": 6,
          "icon": "lucide:file-text",
          "item_names": [
            "contact_full_name_first_name",
            "contact_full_name_last_name",
            "contact_address",
          ],
          "items": {
            "contact_address": {
              "span": 12,
            },
          },
          "label": "ข้อมูลติดต่อ",
          "span": 12,
          "type": "TableCard",
        },
        "telephone": {
          "can_edit": true,
          "children_span": 6,
          "icon": "lucide:file-text",
          "item_names": [
            "telephone_otp_address",
          ],
          "items": {},
          "label": "Mobile Phone Verification",
          "span": 12,
          "type": "TableCard",
        },
        "utility_bill_electricity": {
          "children_span": 12,
          "item_names": [
            "utility_bill_electricity",
          ],
          "items": {},
          "label": "ใบแจ้งหนี้ค่าไฟฟ้า",
          "span": 4,
          "type": "TableCard",
        },
        "utility_bill_telco": {
          "children_span": 12,
          "item_names": [
            "utility_bill_telco",
          ],
          "items": {},
          "label": "ใบแจ้งหนี้ค่าโทรศัพท์",
          "span": 4,
          "type": "TableCard",
        },
        "utility_bill_water": {
          "children_span": 12,
          "item_names": [
            "utility_bill_water",
          ],
          "items": {},
          "label": "ใบแจ้งหนี้ค่าน้ำประปา",
          "span": 4,
          "type": "TableCard",
        },
      },
      "tab": "Response",
      "type": "Verifio",
    },
  },
  "tabs": {
    "answers": {
      "hide_report": true,
    },
  },
}
`;
