import {
  require_toInteger
} from "./chunk-ZXTARUDF.js";
import "./chunk-3R6DH2NC.js";
import {
  require_isIterateeCall
} from "./chunk-SC7EM6JJ.js";
import "./chunk-YHXMOY7F.js";
import {
  require_baseSlice
} from "./chunk-WI7ETHBW.js";
import "./chunk-Z3AMIQTO.js";
import "./chunk-MIX47OBP.js";
import "./chunk-D5KFZMAO.js";
import "./chunk-EI3ATIN2.js";
import "./chunk-MYGK6PJD.js";
import "./chunk-DAWEUZO3.js";
import "./chunk-MIPDNB3W.js";
import "./chunk-MRQ46THD.js";
import "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/chunk.js
var require_chunk = __commonJS({
  "node_modules/lodash/chunk.js"(exports, module) {
    var baseSlice = require_baseSlice();
    var isIterateeCall = require_isIterateeCall();
    var toInteger = require_toInteger();
    var nativeCeil = Math.ceil;
    var nativeMax = Math.max;
    function chunk(array, size, guard) {
      if (guard ? isIterateeCall(array, size, guard) : size === void 0) {
        size = 1;
      } else {
        size = nativeMax(toInteger(size), 0);
      }
      var length = array == null ? 0 : array.length;
      if (!length || size < 1) {
        return [];
      }
      var index = 0, resIndex = 0, result = Array(nativeCeil(length / size));
      while (index < length) {
        result[resIndex++] = baseSlice(array, index, index += size);
      }
      return result;
    }
    module.exports = chunk;
  }
});
export default require_chunk();
//# sourceMappingURL=lodash_chunk.js.map
