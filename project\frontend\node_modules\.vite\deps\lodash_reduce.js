import {
  require_baseEach
} from "./chunk-AXJC3SCJ.js";
import {
  require_baseIteratee
} from "./chunk-KJTSLONF.js";
import "./chunk-LQ553RKZ.js";
import "./chunk-UVPP7C3Y.js";
import "./chunk-73T7RC44.js";
import "./chunk-AICGIRQS.js";
import "./chunk-U7VWWHCD.js";
import "./chunk-G4H6LCRN.js";
import "./chunk-ILQTQNEH.js";
import "./chunk-QC3FIJ3X.js";
import "./chunk-M2WBRPB3.js";
import "./chunk-VMHZGPRL.js";
import "./chunk-RWOYAFDL.js";
import "./chunk-64Z5HK43.js";
import {
  require_arrayReduce
} from "./chunk-TPPCP22B.js";
import "./chunk-VZITUV5G.js";
import "./chunk-CWSHORJK.js";
import "./chunk-Z3AMIQTO.js";
import "./chunk-YXE4T6UR.js";
import "./chunk-6MOPH6ER.js";
import "./chunk-BVJZN6TR.js";
import "./chunk-WEVAHQUU.js";
import "./chunk-U4XCUM7X.js";
import "./chunk-5PX5EJ7R.js";
import "./chunk-7I5PEIZF.js";
import "./chunk-G6M3KSL2.js";
import "./chunk-F5EYQNHY.js";
import "./chunk-D4QEHMHD.js";
import "./chunk-RWM7CMLN.js";
import "./chunk-GYHWOQRN.js";
import "./chunk-IW7F5ZXM.js";
import "./chunk-F753BK7A.js";
import {
  require_isArray
} from "./chunk-TP2NNXVG.js";
import "./chunk-MIX47OBP.js";
import "./chunk-D5KFZMAO.js";
import "./chunk-EI3ATIN2.js";
import "./chunk-MYGK6PJD.js";
import "./chunk-DAWEUZO3.js";
import "./chunk-MIPDNB3W.js";
import "./chunk-MRQ46THD.js";
import "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/_baseReduce.js
var require_baseReduce = __commonJS({
  "node_modules/lodash/_baseReduce.js"(exports, module) {
    function baseReduce(collection, iteratee, accumulator, initAccum, eachFunc) {
      eachFunc(collection, function(value, index, collection2) {
        accumulator = initAccum ? (initAccum = false, value) : iteratee(accumulator, value, index, collection2);
      });
      return accumulator;
    }
    module.exports = baseReduce;
  }
});

// node_modules/lodash/reduce.js
var require_reduce = __commonJS({
  "node_modules/lodash/reduce.js"(exports, module) {
    var arrayReduce = require_arrayReduce();
    var baseEach = require_baseEach();
    var baseIteratee = require_baseIteratee();
    var baseReduce = require_baseReduce();
    var isArray = require_isArray();
    function reduce(collection, iteratee, accumulator) {
      var func = isArray(collection) ? arrayReduce : baseReduce, initAccum = arguments.length < 3;
      return func(collection, baseIteratee(iteratee, 4), accumulator, initAccum, baseEach);
    }
    module.exports = reduce;
  }
});
export default require_reduce();
//# sourceMappingURL=lodash_reduce.js.map
