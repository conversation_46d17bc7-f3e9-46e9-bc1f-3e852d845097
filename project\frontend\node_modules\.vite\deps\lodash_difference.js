import {
  require_baseFlatten
} from "./chunk-ICXN6OJ6.js";
import {
  require_arrayIncludes,
  require_arrayIncludesWith
} from "./chunk-TYXAQETL.js";
import {
  require_isArrayLikeObject
} from "./chunk-DOSSVMDF.js";
import {
  require_baseRest
} from "./chunk-QYSLMRXE.js";
import "./chunk-6U5UHEYX.js";
import {
  require_SetCache,
  require_cacheHas
} from "./chunk-M2WBRPB3.js";
import "./chunk-JIR7Y6MV.js";
import "./chunk-64Z5HK43.js";
import {
  require_arrayMap
} from "./chunk-CWSHORJK.js";
import "./chunk-U4XCUM7X.js";
import "./chunk-3HWTEJRL.js";
import "./chunk-5PX5EJ7R.js";
import "./chunk-7I5PEIZF.js";
import "./chunk-IW7F5ZXM.js";
import {
  require_baseUnary
} from "./chunk-F753BK7A.js";
import "./chunk-TP2NNXVG.js";
import "./chunk-MIX47OBP.js";
import "./chunk-EI3ATIN2.js";
import "./chunk-MYGK6PJD.js";
import "./chunk-DAWEUZO3.js";
import "./chunk-MIPDNB3W.js";
import "./chunk-MRQ46THD.js";
import "./chunk-RYVM6PZ4.js";
import "./chunk-4R5BXC4A.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/lodash/_baseDifference.js
var require_baseDifference = __commonJS({
  "node_modules/lodash/_baseDifference.js"(exports, module) {
    var SetCache = require_SetCache();
    var arrayIncludes = require_arrayIncludes();
    var arrayIncludesWith = require_arrayIncludesWith();
    var arrayMap = require_arrayMap();
    var baseUnary = require_baseUnary();
    var cacheHas = require_cacheHas();
    var LARGE_ARRAY_SIZE = 200;
    function baseDifference(array, values, iteratee, comparator) {
      var index = -1, includes = arrayIncludes, isCommon = true, length = array.length, result = [], valuesLength = values.length;
      if (!length) {
        return result;
      }
      if (iteratee) {
        values = arrayMap(values, baseUnary(iteratee));
      }
      if (comparator) {
        includes = arrayIncludesWith;
        isCommon = false;
      } else if (values.length >= LARGE_ARRAY_SIZE) {
        includes = cacheHas;
        isCommon = false;
        values = new SetCache(values);
      }
      outer:
        while (++index < length) {
          var value = array[index], computed = iteratee == null ? value : iteratee(value);
          value = comparator || value !== 0 ? value : 0;
          if (isCommon && computed === computed) {
            var valuesIndex = valuesLength;
            while (valuesIndex--) {
              if (values[valuesIndex] === computed) {
                continue outer;
              }
            }
            result.push(value);
          } else if (!includes(values, computed, comparator)) {
            result.push(value);
          }
        }
      return result;
    }
    module.exports = baseDifference;
  }
});

// node_modules/lodash/difference.js
var require_difference = __commonJS({
  "node_modules/lodash/difference.js"(exports, module) {
    var baseDifference = require_baseDifference();
    var baseFlatten = require_baseFlatten();
    var baseRest = require_baseRest();
    var isArrayLikeObject = require_isArrayLikeObject();
    var difference = baseRest(function(array, values) {
      return isArrayLikeObject(array) ? baseDifference(array, baseFlatten(values, 1, isArrayLikeObject, true)) : [];
    });
    module.exports = difference;
  }
});
export default require_difference();
//# sourceMappingURL=lodash_difference.js.map
