{"version": 3, "sources": ["../../lodash/get.js"], "sourcesContent": ["var baseGet = require('./_baseGet');\n\n/**\n * Gets the value at `path` of `object`. If the resolved value is\n * `undefined`, the `defaultValue` is returned in its place.\n *\n * @static\n * @memberOf _\n * @since 3.7.0\n * @category Object\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the property to get.\n * @param {*} [defaultValue] The value returned for `undefined` resolved values.\n * @returns {*} Returns the resolved value.\n * @example\n *\n * var object = { 'a': [{ 'b': { 'c': 3 } }] };\n *\n * _.get(object, 'a[0].b.c');\n * // => 3\n *\n * _.get(object, ['a', '0', 'b', 'c']);\n * // => 3\n *\n * _.get(object, 'a.b.c', 'default');\n * // => 'default'\n */\nfunction get(object, path, defaultValue) {\n  var result = object == null ? undefined : baseGet(object, path);\n  return result === undefined ? defaultValue : result;\n}\n\nmodule.exports = get;\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,QAAI,UAAU;AA2Bd,aAAS,IAAI,QAAQ,MAAM,cAAc;AACvC,UAAI,SAAS,UAAU,OAAO,SAAY,QAAQ,QAAQ,IAAI;AAC9D,aAAO,WAAW,SAAY,eAAe;AAAA,IAC/C;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}