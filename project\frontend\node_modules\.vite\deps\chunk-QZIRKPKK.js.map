{"version": 3, "sources": ["../../lodash/_castSlice.js", "../../lodash/_asciiToArray.js", "../../lodash/_unicodeToArray.js", "../../lodash/_stringToArray.js", "../../lodash/_createCaseFirst.js", "../../lodash/upperFirst.js"], "sourcesContent": ["var baseSlice = require('./_baseSlice');\n\n/**\n * Casts `array` to a slice if it's needed.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {number} start The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns the cast slice.\n */\nfunction castSlice(array, start, end) {\n  var length = array.length;\n  end = end === undefined ? length : end;\n  return (!start && end >= length) ? array : baseSlice(array, start, end);\n}\n\nmodule.exports = castSlice;\n", "/**\n * Converts an ASCII `string` to an array.\n *\n * @private\n * @param {string} string The string to convert.\n * @returns {Array} Returns the converted array.\n */\nfunction asciiToArray(string) {\n  return string.split('');\n}\n\nmodule.exports = asciiToArray;\n", "/** Used to compose unicode character classes. */\nvar rsAstralRange = '\\\\ud800-\\\\udfff',\n    rsComboMarksRange = '\\\\u0300-\\\\u036f',\n    reComboHalfMarksRange = '\\\\ufe20-\\\\ufe2f',\n    rsComboSymbolsRange = '\\\\u20d0-\\\\u20ff',\n    rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange,\n    rsVarRange = '\\\\ufe0e\\\\ufe0f';\n\n/** Used to compose unicode capture groups. */\nvar rsAstral = '[' + rsAstralRange + ']',\n    rsCombo = '[' + rsComboRange + ']',\n    rsFitz = '\\\\ud83c[\\\\udffb-\\\\udfff]',\n    rsModifier = '(?:' + rsCombo + '|' + rsFitz + ')',\n    rsNonAstral = '[^' + rsAstralRange + ']',\n    rsRegional = '(?:\\\\ud83c[\\\\udde6-\\\\uddff]){2}',\n    rsSurrPair = '[\\\\ud800-\\\\udbff][\\\\udc00-\\\\udfff]',\n    rsZWJ = '\\\\u200d';\n\n/** Used to compose unicode regexes. */\nvar reOptMod = rsModifier + '?',\n    rsOptVar = '[' + rsVarRange + ']?',\n    rsOptJoin = '(?:' + rsZWJ + '(?:' + [rsNonAstral, rsRegional, rsSurrPair].join('|') + ')' + rsOptVar + reOptMod + ')*',\n    rsSeq = rsOptVar + reOptMod + rsOptJoin,\n    rsSymbol = '(?:' + [rsNonAstral + rsCombo + '?', rsCombo, rsRegional, rsSurrPair, rsAstral].join('|') + ')';\n\n/** Used to match [string symbols](https://mathiasbynens.be/notes/javascript-unicode). */\nvar reUnicode = RegExp(rsFitz + '(?=' + rsFitz + ')|' + rsSymbol + rsSeq, 'g');\n\n/**\n * Converts a Unicode `string` to an array.\n *\n * @private\n * @param {string} string The string to convert.\n * @returns {Array} Returns the converted array.\n */\nfunction unicodeToArray(string) {\n  return string.match(reUnicode) || [];\n}\n\nmodule.exports = unicodeToArray;\n", "var asciiToArray = require('./_asciiToArray'),\n    hasUnicode = require('./_hasUnicode'),\n    unicodeToArray = require('./_unicodeToArray');\n\n/**\n * Converts `string` to an array.\n *\n * @private\n * @param {string} string The string to convert.\n * @returns {Array} Returns the converted array.\n */\nfunction stringToArray(string) {\n  return hasUnicode(string)\n    ? unicodeToArray(string)\n    : asciiToArray(string);\n}\n\nmodule.exports = stringToArray;\n", "var castSlice = require('./_castSlice'),\n    hasUnicode = require('./_hasUnicode'),\n    stringToArray = require('./_stringToArray'),\n    toString = require('./toString');\n\n/**\n * Creates a function like `_.lowerFirst`.\n *\n * @private\n * @param {string} methodName The name of the `String` case method to use.\n * @returns {Function} Returns the new case function.\n */\nfunction createCaseFirst(methodName) {\n  return function(string) {\n    string = toString(string);\n\n    var strSymbols = hasUnicode(string)\n      ? stringToArray(string)\n      : undefined;\n\n    var chr = strSymbols\n      ? strSymbols[0]\n      : string.charAt(0);\n\n    var trailing = strSymbols\n      ? castSlice(strSymbols, 1).join('')\n      : string.slice(1);\n\n    return chr[methodName]() + trailing;\n  };\n}\n\nmodule.exports = createCaseFirst;\n", "var createCaseFirst = require('./_createCaseFirst');\n\n/**\n * Converts the first character of `string` to upper case.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the converted string.\n * @example\n *\n * _.upperFirst('fred');\n * // => 'Fred'\n *\n * _.upperFirst('FRED');\n * // => 'FRED'\n */\nvar upperFirst = createCaseFirst('toUpperCase');\n\nmodule.exports = upperFirst;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,YAAY;AAWhB,aAAS,UAAU,OAAO,OAAO,KAAK;AACpC,UAAI,SAAS,MAAM;AACnB,YAAM,QAAQ,SAAY,SAAS;AACnC,aAAQ,CAAC,SAAS,OAAO,SAAU,QAAQ,UAAU,OAAO,OAAO,GAAG;AAAA,IACxE;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAOA,aAAS,aAAa,QAAQ;AAC5B,aAAO,OAAO,MAAM,EAAE;AAAA,IACxB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACXjB;AAAA;AACA,QAAI,gBAAgB;AAApB,QACI,oBAAoB;AADxB,QAEI,wBAAwB;AAF5B,QAGI,sBAAsB;AAH1B,QAII,eAAe,oBAAoB,wBAAwB;AAJ/D,QAKI,aAAa;AAGjB,QAAI,WAAW,MAAM,gBAAgB;AAArC,QACI,UAAU,MAAM,eAAe;AADnC,QAEI,SAAS;AAFb,QAGI,aAAa,QAAQ,UAAU,MAAM,SAAS;AAHlD,QAII,cAAc,OAAO,gBAAgB;AAJzC,QAKI,aAAa;AALjB,QAMI,aAAa;AANjB,QAOI,QAAQ;AAGZ,QAAI,WAAW,aAAa;AAA5B,QACI,WAAW,MAAM,aAAa;AADlC,QAEI,YAAY,QAAQ,QAAQ,QAAQ,CAAC,aAAa,YAAY,UAAU,EAAE,KAAK,GAAG,IAAI,MAAM,WAAW,WAAW;AAFtH,QAGI,QAAQ,WAAW,WAAW;AAHlC,QAII,WAAW,QAAQ,CAAC,cAAc,UAAU,KAAK,SAAS,YAAY,YAAY,QAAQ,EAAE,KAAK,GAAG,IAAI;AAG5G,QAAI,YAAY,OAAO,SAAS,QAAQ,SAAS,OAAO,WAAW,OAAO,GAAG;AAS7E,aAAS,eAAe,QAAQ;AAC9B,aAAO,OAAO,MAAM,SAAS,KAAK,CAAC;AAAA,IACrC;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACvCjB;AAAA;AAAA,QAAI,eAAe;AAAnB,QACI,aAAa;AADjB,QAEI,iBAAiB;AASrB,aAAS,cAAc,QAAQ;AAC7B,aAAO,WAAW,MAAM,IACpB,eAAe,MAAM,IACrB,aAAa,MAAM;AAAA,IACzB;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,aAAa;AADjB,QAEI,gBAAgB;AAFpB,QAGI,WAAW;AASf,aAAS,gBAAgB,YAAY;AACnC,aAAO,SAAS,QAAQ;AACtB,iBAAS,SAAS,MAAM;AAExB,YAAI,aAAa,WAAW,MAAM,IAC9B,cAAc,MAAM,IACpB;AAEJ,YAAI,MAAM,aACN,WAAW,CAAC,IACZ,OAAO,OAAO,CAAC;AAEnB,YAAI,WAAW,aACX,UAAU,YAAY,CAAC,EAAE,KAAK,EAAE,IAChC,OAAO,MAAM,CAAC;AAElB,eAAO,IAAI,UAAU,EAAE,IAAI;AAAA,MAC7B;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChCjB;AAAA;AAAA,QAAI,kBAAkB;AAmBtB,QAAI,aAAa,gBAAgB,aAAa;AAE9C,WAAO,UAAU;AAAA;AAAA;", "names": []}